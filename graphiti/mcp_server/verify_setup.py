#!/usr/bin/env python3
"""
Verification script for Graphiti MCP Server setup
Tests the server configuration and connectivity
"""

import asyncio
import json
import os
import sys
from pathlib import Path

import aiohttp


async def check_neo4j_health():
    """Check if Neo4j is healthy and accessible."""
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:7474') as response:
                if response.status == 200:
                    print("✅ Neo4j is running and accessible")
                    return True
                else:
                    print(f"❌ Neo4j returned status {response.status}")
                    return False
    except Exception as e:
        print(f"❌ Neo4j connection failed: {e}")
        return False


async def check_mcp_server_health():
    """Check if MCP server is healthy and accessible."""
    try:
        async with aiohttp.ClientSession() as session:
            # Try to connect to the SSE endpoint
            async with session.get('http://localhost:8089/sse') as response:
                if response.status in [200, 404]:  # 404 is expected for SSE endpoint without proper headers
                    print("✅ MCP Server is running and accessible")
                    return True
                else:
                    print(f"❌ MCP Server returned status {response.status}")
                    return False
    except Exception as e:
        print(f"❌ MCP Server connection failed: {e}")
        return False


def check_environment_config():
    """Check if environment variables are properly configured."""
    print("🔍 Checking environment configuration...")
    
    env_file = Path('.env')
    if not env_file.exists():
        print("❌ .env file not found")
        return False
    
    # Load environment variables from .env file
    with open(env_file) as f:
        env_content = f.read()
    
    required_vars = [
        'NEO4J_URI',
        'NEO4J_USER', 
        'NEO4J_PASSWORD',
        'GOOGLE_API_KEY',
        'MODEL_NAME'
    ]
    
    missing_vars = []
    for var in required_vars:
        if var not in env_content or f'{var}=' not in env_content:
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        return False
    
    print("✅ Environment configuration looks good")
    return True


async def test_gemini_api():
    """Test if Gemini API is accessible with the configured credentials."""
    try:
        # This is a basic connectivity test
        # In a real scenario, you might want to make an actual API call
        print("🔍 Testing Gemini API configuration...")
        
        # Check if the API key is set
        env_file = Path('.env')
        if env_file.exists():
            with open(env_file) as f:
                content = f.read()
                if 'GOOGLE_API_KEY=' in content and 'Aqyx2019@' in content:
                    print("✅ Gemini API key is configured")
                    return True
                else:
                    print("❌ Gemini API key not found in configuration")
                    return False
        
        return False
    except Exception as e:
        print(f"❌ Gemini API test failed: {e}")
        return False


async def main():
    """Run all verification checks."""
    print("🧪 Graphiti MCP Server Setup Verification")
    print("=" * 50)
    
    # Change to the script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    checks = [
        ("Environment Configuration", check_environment_config()),
        ("Gemini API Configuration", test_gemini_api()),
        ("Neo4j Health", check_neo4j_health()),
        ("MCP Server Health", check_mcp_server_health()),
    ]
    
    results = []
    for name, check in checks:
        print(f"\n🔍 {name}...")
        if asyncio.iscoroutine(check):
            result = await check
        else:
            result = check
        results.append((name, result))
    
    print("\n" + "=" * 50)
    print("📋 Verification Summary:")
    
    all_passed = True
    for name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {name}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 All checks passed! Your Graphiti MCP Server is ready to use.")
        print("🔗 MCP Server URL: http://localhost:8089/sse")
        print("🔗 Neo4j Browser: http://localhost:7474")
    else:
        print("\n⚠️  Some checks failed. Please review the configuration and try again.")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
