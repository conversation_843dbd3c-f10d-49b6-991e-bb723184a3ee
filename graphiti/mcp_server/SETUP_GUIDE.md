# Graphiti MCP Server with Gemini Setup Guide

本指南将帮助您搭建一个完整的 Graphiti MCP 服务器环境，使用 Google Gemini 作为 LLM 提供商。

## 🎯 配置概览

- **MCP 服务器端口**: 8089 (外部访问)
- **传输方式**: SSE (Server-Sent Events)
- **数据库**: Neo4j (Docker 容器)
- **LLM 提供商**: Google Gemini
- **模型**: gemini-2.0-flash-001
- **API 基础 URL**: http://bt.itzxc.cn:8000

## 📋 前置要求

- Docker 和 Docker Compose
- 至少 4GB 可用内存
- 端口 8089 和 7474、7687 可用

## 🚀 快速启动

### 1. 启动服务

```bash
cd graphiti/mcp_server
./start_server.sh
```

### 2. 验证安装

```bash
python verify_setup.py
```

### 3. 访问服务

- **MCP 服务器**: http://localhost:8089/sse
- **Neo4j 浏览器**: http://localhost:7474 (用户名: neo4j, 密码: demodemo)

## 🔧 详细配置

### 环境变量配置

`.env` 文件包含以下配置：

```env
# Neo4j 数据库配置
NEO4J_URI=bolt://neo4j:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=demodemo

# Google Gemini API 配置
GOOGLE_API_KEY=Aqyx2019@
GEMINI_BASE_URL=http://bt.itzxc.cn:8000
MODEL_NAME=gemini-2.0-flash-001
SMALL_MODEL_NAME=gemini-2.0-flash-001
EMBEDDER_MODEL_NAME=text-embedding-001

# 性能配置
SEMAPHORE_LIMIT=10
LLM_TEMPERATURE=0.0
```

### Docker Compose 配置

服务包括：
- **neo4j**: Neo4j 图数据库
- **graphiti-mcp**: Graphiti MCP 服务器

端口映射：
- 8089:8000 (MCP 服务器)
- 7474:7474 (Neo4j HTTP)
- 7687:7687 (Neo4j Bolt)

## 🔌 MCP 客户端集成

### Claude Desktop 配置

将以下配置添加到 Claude Desktop 的配置文件中：

```json
{
  "mcpServers": {
    "graphiti-memory": {
      "transport": "sse",
      "url": "http://localhost:8089/sse"
    }
  }
}
```

### Cursor IDE 配置

在 Cursor 的 MCP 设置中添加：

```json
{
  "mcpServers": {
    "graphiti-memory": {
      "url": "http://localhost:8089/sse"
    }
  }
}
```

## 🛠️ 手动启动步骤

如果您不想使用启动脚本，可以手动执行以下步骤：

### 1. 构建并启动容器

```bash
cd graphiti/mcp_server
docker compose up --build
```

### 2. 检查服务状态

```bash
# 检查容器状态
docker compose ps

# 查看日志
docker compose logs graphiti-mcp
docker compose logs neo4j
```

## 🔍 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 检查端口占用
   lsof -i :8089
   lsof -i :7474
   lsof -i :7687
   ```

2. **内存不足**
   - 确保至少有 4GB 可用内存
   - 可以在 docker-compose.yml 中调整 Neo4j 内存设置

3. **API 连接问题**
   - 验证 Gemini API 密钥和基础 URL
   - 检查网络连接

### 日志查看

```bash
# 查看 MCP 服务器日志
docker compose logs -f graphiti-mcp

# 查看 Neo4j 日志
docker compose logs -f neo4j
```

## 🧪 测试 API

### 基本连接测试

```bash
# 测试 MCP 服务器
curl http://localhost:8089/sse

# 测试 Neo4j
curl http://localhost:7474
```

### 使用验证脚本

```bash
python verify_setup.py
```

## 📚 可用工具

MCP 服务器提供以下工具：

- `add_memory`: 添加 episode 到知识图谱
- `search_memory_nodes`: 搜索节点摘要
- `search_memory_facts`: 搜索事实关系
- `delete_entity_edge`: 删除实体边
- `delete_episode`: 删除 episode
- `get_entity_edge`: 获取实体边
- `get_episodes`: 获取最近的 episodes
- `clear_graph`: 清除图数据

## 🔄 停止服务

```bash
# 停止服务
docker compose down

# 停止并删除数据卷
docker compose down -v
```

## 📞 支持

如果遇到问题，请检查：
1. Docker 和 Docker Compose 版本
2. 端口可用性
3. 环境变量配置
4. 网络连接

更多信息请参考 [Graphiti 官方文档](https://help.getzep.com/graphiti)。
