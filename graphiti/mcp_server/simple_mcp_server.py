#!/usr/bin/env python3
"""
Simplified Graphiti MCP Server for testing purposes
This version uses minimal dependencies and can run without complex setup
"""

import asyncio
import json
import logging
import os
import sys
from typing import Any, Dict, List, Optional

# Simple HTTP server for SSE
from http.server import HTTPServer, BaseHTTPRequestHandler
import threading
import time

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GraphitiMCPHandler(BaseHTTPRequestHandler):
    """Simple HTTP handler for MCP SSE transport"""
    
    def do_GET(self):
        """Handle GET requests for SSE endpoint"""
        if self.path == '/sse':
            self.send_response(200)
            self.send_header('Content-Type', 'text/event-stream')
            self.send_header('Cache-Control', 'no-cache')
            self.send_header('Connection', 'keep-alive')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            # Send initial connection message
            self.send_sse_message('connected', {'status': 'ready'})
            
            # Keep connection alive
            try:
                while True:
                    time.sleep(30)
                    self.send_sse_message('ping', {'timestamp': time.time()})
            except:
                pass
        else:
            self.send_response(404)
            self.end_headers()
    
    def do_POST(self):
        """Handle POST requests for MCP commands"""
        if self.path == '/sse':
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            
            try:
                request = json.loads(post_data.decode('utf-8'))
                response = self.handle_mcp_request(request)
                
                self.send_response(200)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps(response).encode('utf-8'))
            except Exception as e:
                logger.error(f"Error handling request: {e}")
                self.send_response(500)
                self.end_headers()
        else:
            self.send_response(404)
            self.end_headers()
    
    def send_sse_message(self, event: str, data: Dict[str, Any]):
        """Send Server-Sent Event message"""
        try:
            message = f"event: {event}\ndata: {json.dumps(data)}\n\n"
            self.wfile.write(message.encode('utf-8'))
            self.wfile.flush()
        except:
            pass
    
    def handle_mcp_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """Handle MCP protocol requests"""
        method = request.get('method', '')
        params = request.get('params', {})
        
        # Mock responses for testing
        if method == 'tools/list':
            return {
                'tools': [
                    {
                        'name': 'add_memory',
                        'description': 'Add an episode to memory',
                        'inputSchema': {
                            'type': 'object',
                            'properties': {
                                'name': {'type': 'string'},
                                'episode_body': {'type': 'string'}
                            },
                            'required': ['name', 'episode_body']
                        }
                    },
                    {
                        'name': 'search_memory_nodes',
                        'description': 'Search memory nodes',
                        'inputSchema': {
                            'type': 'object',
                            'properties': {
                                'query': {'type': 'string'}
                            },
                            'required': ['query']
                        }
                    }
                ]
            }
        elif method == 'tools/call':
            tool_name = params.get('name', '')
            arguments = params.get('arguments', {})
            
            if tool_name == 'add_memory':
                return {
                    'content': [
                        {
                            'type': 'text',
                            'text': f"Memory added: {arguments.get('name', 'Unknown')}"
                        }
                    ]
                }
            elif tool_name == 'search_memory_nodes':
                return {
                    'content': [
                        {
                            'type': 'text',
                            'text': f"Search results for: {arguments.get('query', 'Unknown')}\n(This is a mock response - Neo4j not connected)"
                        }
                    ]
                }
        
        return {'error': f'Unknown method: {method}'}
    
    def log_message(self, format, *args):
        """Override to use our logger"""
        logger.info(f"{self.address_string()} - {format % args}")


def run_server(port: int = 8089):
    """Run the simple MCP server"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, GraphitiMCPHandler)
    
    logger.info(f"🚀 Simplified Graphiti MCP Server starting on port {port}")
    logger.info(f"📊 Server URL: http://localhost:{port}/sse")
    logger.info("⚠️  This is a simplified version for testing purposes")
    logger.info("🔧 For full functionality, please resolve the Docker/network issues")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
        httpd.shutdown()


if __name__ == "__main__":
    # Check environment variables
    port = int(os.environ.get('MCP_SERVER_PORT', '8089'))
    
    # Display configuration
    logger.info("📋 Configuration:")
    logger.info(f"  Port: {port}")
    logger.info(f"  Gemini API Key: {'✅ Set' if os.environ.get('GOOGLE_API_KEY') else '❌ Not set'}")
    logger.info(f"  Neo4j URI: {os.environ.get('NEO4J_URI', 'Not set')}")
    
    run_server(port)
