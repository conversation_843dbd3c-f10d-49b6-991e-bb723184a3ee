#!/bin/bash

# Graphiti MCP Server Startup Script
# This script starts the complete Graphiti MCP server environment with Neo4j and Gemini configuration

echo "🚀 Starting Graphiti MCP Server with Gemini and Neo4j..."
echo "📊 Server will be available at: http://localhost:8089/sse"
echo "🗄️  Neo4j will be available at: http://localhost:7474"
echo ""

# Check if Docker and Docker Compose are available
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose is not available. Please install Docker Compose first."
    exit 1
fi

# Navigate to the MCP server directory
cd "$(dirname "$0")"

# Check if .env file exists
if [ ! -f .env ]; then
    echo "⚠️  .env file not found. Creating default configuration..."
    cp .env.example .env 2>/dev/null || echo "Please create a .env file with your configuration."
fi

# Start the services
echo "🔧 Starting services with <PERSON>er Compose..."
if command -v docker-compose &> /dev/null; then
    docker-compose up --build
else
    docker compose up --build
fi
