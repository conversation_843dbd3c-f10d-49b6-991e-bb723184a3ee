#!/usr/bin/env python3
"""
简化的验证脚本，使用标准库检查服务状态
"""

import os
import socket
import urllib.request
import json

def check_port(host, port):
    """检查端口是否开放"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except:
        return False

def check_http_service(url):
    """检查HTTP服务是否响应"""
    try:
        response = urllib.request.urlopen(url, timeout=5)
        return response.status == 200
    except:
        return False

def main():
    print("🧪 Graphiti MCP 服务器验证")
    print("=" * 50)
    
    # 检查环境变量
    print("🔍 环境变量检查:")
    google_key = os.environ.get('GOOGLE_API_KEY')
    if google_key:
        print(f"  ✅ GOOGLE_API_KEY: 已设置")
    else:
        print(f"  ❌ GOOGLE_API_KEY: 未设置")
    
    # 检查 Neo4j 端口
    print("\n🔍 Neo4j 连接检查:")
    neo4j_bolt_ok = check_port('localhost', 7687)
    neo4j_http_ok = check_port('localhost', 7474)
    
    if neo4j_bolt_ok:
        print("  ✅ Neo4j Bolt 端口 (7687): 可访问")
    else:
        print("  ❌ Neo4j Bolt 端口 (7687): 不可访问")
    
    if neo4j_http_ok:
        print("  ✅ Neo4j HTTP 端口 (7474): 可访问")
        # 检查 Neo4j HTTP API
        neo4j_api_ok = check_http_service('http://localhost:7474')
        if neo4j_api_ok:
            print("  ✅ Neo4j HTTP API: 正常响应")
        else:
            print("  ❌ Neo4j HTTP API: 无响应")
    else:
        print("  ❌ Neo4j HTTP 端口 (7474): 不可访问")
    
    # 检查 MCP 服务器端口
    print("\n🔍 MCP 服务器检查:")
    mcp_port_ok = check_port('localhost', 8089)
    
    if mcp_port_ok:
        print("  ✅ MCP 服务器端口 (8089): 可访问")
    else:
        print("  ❌ MCP 服务器端口 (8089): 不可访问")
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 验证总结:")
    
    if google_key and neo4j_bolt_ok and neo4j_http_ok and mcp_port_ok:
        print("🎉 所有检查都通过了！")
        print("🔗 MCP 服务器 URL: http://localhost:8089/sse")
        print("🔗 Neo4j 浏览器: http://localhost:7474")
        print("📝 用户名: neo4j, 密码: demodemo")
        print("\n📚 MCP 客户端配置示例:")
        print("""
{
  "mcpServers": {
    "graphiti-memory": {
      "transport": "sse",
      "url": "http://localhost:8089/sse"
    }
  }
}
        """)
    else:
        print("⚠️  部分检查失败，请检查配置")

if __name__ == "__main__":
    main()
